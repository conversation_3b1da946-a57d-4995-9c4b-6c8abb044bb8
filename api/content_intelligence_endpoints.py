"""
Content Intelligence and Market Analytics API Endpoints
Phase 7: Advanced NLP, Sentiment Analysis, and Market Intelligence
"""
from fastapi import APIRouter, Depends, HTTPException, Query, BackgroundTasks
from sqlalchemy.orm import Session
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
from pydantic import BaseModel

from models.base import get_db
from models.gaming import Article, Source
from services.content_intelligence import (
    gaming_content_classifier,
    sentiment_scoring_engine,
    trend_detection_engine,
    market_intelligence_engine,
    entity_recognition_engine,
    GamingContentCategory,
    SentimentCategory
)
from services.market_analytics import (
    gaming_sector_analyzer,
    investment_tracker,
    market_alert_system,
    InvestmentMetrics,
    MarketAlert,
    AlertType
)
from services.competitive_analysis import competitive_analysis_engine

router = APIRouter(prefix="/content-intelligence", tags=["Content Intelligence"])


# Request/Response Models
class ContentAnalysisRequest(BaseModel):
    title: str
    content: str = ""
    summary: str = ""
    source: Optional[str] = None


class BatchContentAnalysisRequest(BaseModel):
    content_items: List[ContentAnalysisRequest]
    timeframe: str = "24h"


class ContentClassificationResponse(BaseModel):
    primary_category: str
    category_confidence: float
    all_categories: Dict[str, float]
    sentiment_score: float
    sentiment_category: str
    gaming_entities: List[str]
    blockchain_networks: List[str]
    market_signals: Dict[str, Any]
    trend_indicators: Dict[str, float]
    classification_timestamp: datetime


class SentimentAnalysisResponse(BaseModel):
    overall_sentiment: float
    base_sentiment: float
    gaming_sentiment: float
    market_sentiment: float
    community_sentiment: float
    sentiment_confidence: float
    sentiment_category: str
    key_sentiment_drivers: List[str]
    analysis_timestamp: datetime


class TrendAnalysisResponse(BaseModel):
    trend_scores: Dict[str, float]
    market_phase: Dict[str, Any]
    trend_momentum: Dict[str, float]
    emerging_themes: List[Dict[str, Any]]
    correlations: Dict[str, Any]
    timeframe: str
    analysis_timestamp: datetime


class MarketIntelligenceResponse(BaseModel):
    market_sentiment: float
    trend_direction: str
    volatility_score: float
    correlation_signals: Dict[str, float]
    investment_signals: Dict[str, Any]
    competitive_analysis: Dict[str, Any]
    risk_assessment: Dict[str, float]
    analysis_timestamp: datetime


class EntityRecognitionResponse(BaseModel):
    projects: List[str]
    tokens: List[str]
    blockchains: List[str]
    categories: List[str]
    confidence_scores: Dict[str, float]
    entity_contexts: Dict[str, List[str]]
    entity_relationships: Dict[str, Any]
    dominant_ecosystem: Dict[str, Any]


@router.post("/classify", response_model=ContentClassificationResponse)
async def classify_content(request: ContentAnalysisRequest):
    """Classify gaming content with advanced NLP analysis"""
    
    try:
        # Perform content classification
        result = gaming_content_classifier.classify_content(
            title=request.title,
            content=request.content,
            summary=request.summary
        )
        
        return ContentClassificationResponse(
            primary_category=result.primary_category.value,
            category_confidence=result.category_confidence,
            all_categories={cat.value: score for cat, score in result.all_categories.items()},
            sentiment_score=result.sentiment_score,
            sentiment_category=result.sentiment_category.value,
            gaming_entities=result.gaming_entities,
            blockchain_networks=result.blockchain_networks,
            market_signals=result.market_signals,
            trend_indicators=result.trend_indicators,
            classification_timestamp=result.classification_timestamp
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Classification error: {str(e)}")


@router.post("/sentiment", response_model=SentimentAnalysisResponse)
async def analyze_sentiment(request: ContentAnalysisRequest):
    """Analyze gaming community sentiment with enhanced scoring"""
    
    try:
        content_text = f"{request.title} {request.content} {request.summary}".strip()
        
        # Perform sentiment analysis
        result = sentiment_scoring_engine.analyze_gaming_sentiment(content_text)
        
        return SentimentAnalysisResponse(
            overall_sentiment=result['overall_sentiment'],
            base_sentiment=result['base_sentiment'],
            gaming_sentiment=result['gaming_sentiment'],
            market_sentiment=result['market_sentiment'],
            community_sentiment=result['community_sentiment'],
            sentiment_confidence=result['sentiment_confidence'],
            sentiment_category=result['sentiment_category'].value,
            key_sentiment_drivers=result['key_sentiment_drivers'],
            analysis_timestamp=result['analysis_timestamp']
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Sentiment analysis error: {str(e)}")


@router.post("/trends", response_model=TrendAnalysisResponse)
async def analyze_trends(request: BatchContentAnalysisRequest):
    """Analyze market trends and intelligence from content batch"""
    
    try:
        # Extract content texts
        content_batch = []
        for item in request.content_items:
            content_text = f"{item.title} {item.content} {item.summary}".strip()
            content_batch.append(content_text)
        
        # Perform trend analysis
        result = trend_detection_engine.detect_trends(content_batch, request.timeframe)
        
        return TrendAnalysisResponse(
            trend_scores=result['trend_scores'],
            market_phase=result['market_phase'],
            trend_momentum=result['trend_momentum'],
            emerging_themes=result['emerging_themes'],
            correlations=result['correlations'],
            timeframe=result['timeframe'],
            analysis_timestamp=result['analysis_timestamp']
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Trend analysis error: {str(e)}")


@router.post("/market-intelligence", response_model=MarketIntelligenceResponse)
async def analyze_market_intelligence(request: BatchContentAnalysisRequest):
    """Comprehensive market intelligence analysis"""
    
    try:
        # Convert request to content data format
        content_data = []
        for item in request.content_items:
            content_data.append({
                'title': item.title,
                'content': item.content,
                'summary': item.summary,
                'source': item.source
            })
        
        # Perform market intelligence analysis
        result = market_intelligence_engine.analyze_gaming_sector(content_data, request.timeframe)
        
        return MarketIntelligenceResponse(
            market_sentiment=result.market_sentiment,
            trend_direction=result.trend_direction,
            volatility_score=result.volatility_score,
            correlation_signals=result.correlation_signals,
            investment_signals=result.investment_signals,
            competitive_analysis=result.competitive_analysis,
            risk_assessment=result.risk_assessment,
            analysis_timestamp=result.analysis_timestamp
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Market intelligence error: {str(e)}")


@router.post("/entities", response_model=EntityRecognitionResponse)
async def recognize_entities(request: ContentAnalysisRequest):
    """Advanced entity recognition for gaming content"""
    
    try:
        content_text = f"{request.title} {request.content} {request.summary}".strip()
        
        # Perform entity recognition
        result = entity_recognition_engine.recognize_entities(content_text)
        
        return EntityRecognitionResponse(
            projects=result['projects'],
            tokens=result['tokens'],
            blockchains=result['blockchains'],
            categories=result['categories'],
            confidence_scores=result['confidence_scores'],
            entity_contexts=result['entity_contexts'],
            entity_relationships=result['entity_relationships'],
            dominant_ecosystem=result['dominant_ecosystem']
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Entity recognition error: {str(e)}")


@router.get("/analytics/dashboard")
async def get_content_analytics_dashboard(
    timeframe: str = Query("24h", description="Time frame for analysis"),
    category: Optional[str] = Query(None, description="Filter by gaming category"),
    db: Session = Depends(get_db)
):
    """Get comprehensive content analytics dashboard data"""
    
    try:
        # Calculate time range
        if timeframe == "1h":
            time_delta = timedelta(hours=1)
        elif timeframe == "24h":
            time_delta = timedelta(hours=24)
        elif timeframe == "7d":
            time_delta = timedelta(days=7)
        elif timeframe == "30d":
            time_delta = timedelta(days=30)
        else:
            time_delta = timedelta(hours=24)
        
        start_time = datetime.utcnow() - time_delta
        
        # Query recent articles
        query = db.query(Article).filter(Article.published_at >= start_time)
        
        if category:
            # Filter by category if specified
            query = query.filter(Article.raw_data.contains(f'"gaming_categories": ["{category}"'))
        
        articles = query.limit(100).all()
        
        # Prepare content for analysis
        content_data = []
        for article in articles:
            content_data.append({
                'title': article.title,
                'content': article.content or "",
                'summary': article.summary or "",
                'source': article.source.name if article.source else "unknown",
                'published_at': article.published_at
            })
        
        if not content_data:
            return {
                "message": "No content available for analysis",
                "timeframe": timeframe,
                "analysis_timestamp": datetime.utcnow()
            }
        
        # Perform comprehensive analysis
        content_batch = [f"{item['title']} {item['content']} {item['summary']}" for item in content_data]
        
        # Get trend analysis
        trend_analysis = trend_detection_engine.detect_trends(content_batch, timeframe)
        
        # Get market intelligence
        market_analysis = market_intelligence_engine.analyze_gaming_sector(content_data, timeframe)
        
        # Calculate category distribution
        category_distribution = {}
        sentiment_by_category = {}
        
        for item in content_data:
            content_text = f"{item['title']} {item['content']} {item['summary']}"
            
            # Classify content
            classification = gaming_content_classifier.classify_content(
                item['title'], item['content'], item['summary']
            )
            
            category = classification.primary_category.value
            category_distribution[category] = category_distribution.get(category, 0) + 1
            
            if category not in sentiment_by_category:
                sentiment_by_category[category] = []
            sentiment_by_category[category].append(classification.sentiment_score)
        
        # Calculate average sentiment by category
        avg_sentiment_by_category = {}
        for cat, sentiments in sentiment_by_category.items():
            avg_sentiment_by_category[cat] = sum(sentiments) / len(sentiments)
        
        return {
            "summary": {
                "total_articles": len(content_data),
                "timeframe": timeframe,
                "analysis_timestamp": datetime.utcnow()
            },
            "category_distribution": category_distribution,
            "sentiment_analysis": {
                "overall_market_sentiment": market_analysis.market_sentiment,
                "sentiment_by_category": avg_sentiment_by_category,
                "trend_direction": market_analysis.trend_direction
            },
            "trend_analysis": {
                "trend_scores": trend_analysis['trend_scores'],
                "market_phase": trend_analysis['market_phase'],
                "emerging_themes": trend_analysis['emerging_themes'][:5]
            },
            "market_intelligence": {
                "investment_signals": market_analysis.investment_signals,
                "risk_assessment": market_analysis.risk_assessment,
                "competitive_analysis": market_analysis.competitive_analysis
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Dashboard analytics error: {str(e)}")


@router.post("/train-classifier")
async def train_content_classifier(background_tasks: BackgroundTasks):
    """Train the content classification model (background task)"""
    
    try:
        # Add training task to background
        background_tasks.add_task(gaming_content_classifier.train_classifier)
        
        return {
            "message": "Content classifier training started in background",
            "status": "initiated",
            "timestamp": datetime.utcnow()
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Training initiation error: {str(e)}")


@router.get("/health")
async def content_intelligence_health():
    """Health check for content intelligence services"""
    
    try:
        # Test basic functionality
        test_result = gaming_content_classifier.classify_content(
            "Test blockchain gaming article about NFTs and play-to-earn mechanics"
        )
        
        return {
            "status": "healthy",
            "services": {
                "content_classifier": "operational",
                "sentiment_engine": "operational",
                "trend_detection": "operational",
                "market_intelligence": "operational",
                "entity_recognition": "operational"
            },
            "test_classification": {
                "primary_category": test_result.primary_category.value,
                "confidence": test_result.category_confidence
            },
            "timestamp": datetime.utcnow()
        }
        
    except Exception as e:
        return {
            "status": "unhealthy",
            "error": str(e),
            "timestamp": datetime.utcnow()
        }


# Market Analytics Endpoints
@router.get("/market/sector-analysis")
async def get_sector_analysis(timeframe: str = Query("7d", description="Analysis timeframe")):
    """Get gaming sector performance analysis"""

    try:
        analysis = await gaming_sector_analyzer.analyze_cross_protocol_performance(timeframe)
        return analysis

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Sector analysis error: {str(e)}")


@router.post("/market/portfolio-tracking")
async def track_portfolio(portfolio: Dict[str, float]):
    """Track gaming portfolio performance"""

    try:
        tracking_result = await investment_tracker.track_gaming_portfolio(portfolio)
        return tracking_result

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Portfolio tracking error: {str(e)}")


@router.get("/market/alerts")
async def get_market_alerts(
    severity: Optional[str] = Query(None, description="Filter by alert severity"),
    project: Optional[str] = Query(None, description="Filter by project name")
):
    """Get active market alerts"""

    try:
        alerts = market_alert_system.get_active_alerts(severity)

        if project:
            alerts = [alert for alert in alerts if project.lower() in alert.project_name.lower()]

        # Convert alerts to dict format for JSON response
        alert_data = []
        for alert in alerts:
            alert_data.append({
                'alert_type': alert.alert_type.value,
                'project_name': alert.project_name,
                'message': alert.message,
                'severity': alert.severity,
                'data': alert.data,
                'timestamp': alert.timestamp,
                'is_active': alert.is_active
            })

        return {
            'alerts': alert_data,
            'total_count': len(alert_data),
            'summary': market_alert_system.get_alert_summary()
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Market alerts error: {str(e)}")


@router.post("/market/monitor")
async def monitor_projects(projects: List[str]):
    """Monitor projects for market alerts"""

    try:
        new_alerts = await market_alert_system.monitor_market_conditions(projects)

        # Convert alerts to dict format
        alert_data = []
        for alert in new_alerts:
            alert_data.append({
                'alert_type': alert.alert_type.value,
                'project_name': alert.project_name,
                'message': alert.message,
                'severity': alert.severity,
                'data': alert.data,
                'timestamp': alert.timestamp
            })

        return {
            'new_alerts': alert_data,
            'alert_count': len(alert_data),
            'monitored_projects': projects,
            'monitoring_timestamp': datetime.utcnow()
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Project monitoring error: {str(e)}")


# Competitive Analysis Endpoints
@router.get("/competitive/landscape")
async def get_competitive_landscape(
    projects: Optional[List[str]] = Query(None, description="Focus on specific projects")
):
    """Get comprehensive competitive landscape analysis"""

    try:
        analysis = await competitive_analysis_engine.analyze_competitive_landscape(projects)
        return analysis

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Competitive analysis error: {str(e)}")


@router.get("/competitive/compare/{project_a}/{project_b}")
async def compare_projects(project_a: str, project_b: str):
    """Compare two gaming projects directly"""

    try:
        # Get competitive scores for both projects
        landscape = await competitive_analysis_engine.analyze_competitive_landscape([project_a, project_b])

        if project_a not in landscape['competitive_scores'] or project_b not in landscape['competitive_scores']:
            raise HTTPException(status_code=404, detail="One or both projects not found")

        scores_a = landscape['competitive_scores'][project_a]
        scores_b = landscape['competitive_scores'][project_b]

        # Calculate comparison metrics
        comparison_metrics = {}
        winner_count_a = 0
        winner_count_b = 0

        for metric in scores_a.keys():
            score_a = scores_a[metric]
            score_b = scores_b[metric]

            comparison_metrics[metric] = {
                f'{project_a}_score': score_a,
                f'{project_b}_score': score_b,
                'winner': project_a if score_a > score_b else project_b,
                'margin': abs(score_a - score_b)
            }

            if score_a > score_b:
                winner_count_a += 1
            else:
                winner_count_b += 1

        overall_winner = project_a if winner_count_a > winner_count_b else project_b

        # Get project names
        project_a_name = competitive_analysis_engine.gaming_projects.get(project_a, {}).get('name', project_a)
        project_b_name = competitive_analysis_engine.gaming_projects.get(project_b, {}).get('name', project_b)

        return {
            'project_a': project_a_name,
            'project_b': project_b_name,
            'comparison_metrics': comparison_metrics,
            'overall_winner': competitive_analysis_engine.gaming_projects.get(overall_winner, {}).get('name', overall_winner),
            'winner_metrics_count': {
                project_a_name: winner_count_a,
                project_b_name: winner_count_b
            },
            'comparison_timestamp': datetime.utcnow()
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Project comparison error: {str(e)}")


@router.get("/competitive/rankings")
async def get_competitive_rankings():
    """Get current competitive rankings of gaming projects"""

    try:
        landscape = await competitive_analysis_engine.analyze_competitive_landscape()

        # Format rankings with project names
        formatted_rankings = []
        for i, (project_id, score) in enumerate(landscape['rankings']):
            project_name = competitive_analysis_engine.gaming_projects.get(project_id, {}).get('name', project_id)
            formatted_rankings.append({
                'rank': i + 1,
                'project_id': project_id,
                'project_name': project_name,
                'competitive_score': score,
                'category': competitive_analysis_engine.gaming_projects.get(project_id, {}).get('category', 'unknown')
            })

        return {
            'rankings': formatted_rankings,
            'market_dynamics': landscape['market_dynamics'],
            'competitive_insights': landscape['competitive_insights'],
            'rankings_timestamp': datetime.utcnow()
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Rankings error: {str(e)}")
