"""
Configuration settings for Web3 Gaming News Tracker
"""
import os
from typing import List, Dict, Any
from pydantic_settings import BaseSettings
from pydantic import Field, validator
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()


class DatabaseSettings(BaseSettings):
    """Database configuration"""
    url: str = Field(default="postgresql://user:password@localhost:5432/web3_gaming_news")
    host: str = Field(default="localhost")
    port: int = Field(default=5432)
    name: str = Field(default="web3_gaming_news")
    user: str = Field(default="user")
    password: str = Field(default="password")
    
    class Config:
        env_prefix = "DATABASE_"


class RedisSettings(BaseSettings):
    """Redis configuration"""
    url: str = Field(default="redis://localhost:6379/0")
    host: str = Field(default="localhost")
    port: int = Field(default=6379)
    db: int = Field(default=0)
    
    class Config:
        env_prefix = "REDIS_"


class APISettings(BaseSettings):
    """API configuration"""
    host: str = Field(default="0.0.0.0")
    port: int = Field(default=8000)
    workers: int = Field(default=4)
    debug: bool = Field(default=True)
    
    class Config:
        env_prefix = "API_"


class BlockchainSettings(BaseSettings):
    """Blockchain RPC configuration"""
    # EVM-compatible chains
    ethereum_rpc_url: str = Field(default="")
    polygon_rpc_url: str = Field(default="")
    bsc_rpc_url: str = Field(default="")
    arbitrum_rpc_url: str = Field(default="")
    optimism_rpc_url: str = Field(default="")
    immutable_rpc_url: str = Field(default="")
    ronin_rpc_url: str = Field(default="")
    base_rpc_url: str = Field(default="")
    avalanche_rpc_url: str = Field(default="")

    # Non-EVM chains
    solana_rpc_url: str = Field(default="https://api.mainnet-beta.solana.com")
    ton_rpc_url: str = Field(default="https://toncenter.com/api/v2/jsonRPC")

    # API Keys for blockchain data providers
    flipside_api_key: str = Field(default="")
    bitquery_api_key: str = Field(default="")
    cryptorank_api_key: str = Field(default="")
    dextools_api_key: str = Field(default="")

    # Connection settings
    request_timeout: int = Field(default=30)
    max_retries: int = Field(default=3)
    block_confirmation_count: int = Field(default=12)
    health_check_interval: int = Field(default=300)  # 5 minutes
    circuit_breaker_threshold: int = Field(default=5)
    circuit_breaker_timeout: int = Field(default=300)  # 5 minutes

    # Sync settings
    sync_interval: int = Field(default=30)  # seconds between sync cycles
    max_blocks_per_sync: int = Field(default=100)  # max blocks to process per sync
    reorg_check_depth: int = Field(default=12)  # blocks to check for reorgs

    class Config:
        env_prefix = ""


class SocialMediaSettings(BaseSettings):
    """Social media API configuration"""
    twitter_bearer_token: str = Field(default="")
    twitter_api_key: str = Field(default="")
    twitter_api_secret: str = Field(default="")
    twitter_access_token: str = Field(default="")
    twitter_access_token_secret: str = Field(default="")
    
    reddit_client_id: str = Field(default="")
    reddit_client_secret: str = Field(default="")
    reddit_user_agent: str = Field(default="Web3GamingNewsBot/1.0")
    
    class Config:
        env_prefix = ""


class NewsAPISettings(BaseSettings):
    """News API configuration"""
    news_api_key: str = Field(default="")
    guardian_api_key: str = Field(default="")
    
    class Config:
        env_prefix = ""


class GamingAPISettings(BaseSettings):
    """Gaming and NFT API configuration"""
    opensea_api_key: str = Field(default="")
    coingecko_api_key: str = Field(default="")
    defipulse_api_key: str = Field(default="")
    dappradar_api_key: str = Field(default="")

    class Config:
        env_prefix = ""


class BlockchainDataSettings(BaseSettings):
    """Blockchain data API configuration"""
    # Flipside Crypto
    flipside_api_key: str = Field(default="")
    flipside_base_url: str = Field(default="https://api.flipsidecrypto.com/api/v2")

    # BitQuery
    bitquery_api_key: str = Field(default="")
    bitquery_access_token: str = Field(default="")
    bitquery_base_url: str = Field(default="https://graphql.bitquery.io")

    # CryptoRank
    cryptorank_api_key: str = Field(default="")
    cryptorank_base_url: str = Field(default="https://api.cryptorank.io/v2")

    # DexTools
    dextools_api_key: str = Field(default="")
    dextools_base_url: str = Field(default="https://api.dextools.io/v1")

    # Dune Analytics
    dune_api_key: str = Field(default="")
    dune_base_url: str = Field(default="https://api.dune.com/api/v1")

    # Blockchain Explorer APIs
    etherscan_api_key: str = Field(default="")
    etherscan_base_url: str = Field(default="https://api.etherscan.io/api")

    solscan_api_key: str = Field(default="")
    solscan_base_url: str = Field(default="https://pro-api.solscan.io/v2.0")
    solscan_public_url: str = Field(default="https://public-api.solscan.io")

    # Reservoir API
    reservoir_api_key: str = Field(default="")
    reservoir_base_url: str = Field(default="https://api.reservoir.tools")

    # Request configuration
    request_timeout: int = Field(default=30)
    max_retries: int = Field(default=3)
    rate_limit_per_minute: int = Field(default=60)

    class Config:
        env_prefix = ""


class ScrapingSettings(BaseSettings):
    """Web scraping configuration"""
    delay: float = Field(default=1.0)
    max_concurrent_requests: int = Field(default=10)
    user_agent: str = Field(default="Web3GamingNewsBot/1.0")
    respect_robots_txt: bool = Field(default=True)
    
    class Config:
        env_prefix = "SCRAPING_"


class RateLimitSettings(BaseSettings):
    """Rate limiting configuration"""
    requests_per_minute: int = Field(default=60)
    burst: int = Field(default=10)
    
    class Config:
        env_prefix = "RATE_LIMIT_"


class CelerySettings(BaseSettings):
    """Celery configuration"""
    broker_url: str = Field(default="redis://localhost:6379/0")
    result_backend: str = Field(default="redis://localhost:6379/0")
    task_serializer: str = Field(default="json")
    accept_content: List[str] = Field(default=["json"])
    result_serializer: str = Field(default="json")
    timezone: str = Field(default="UTC")
    
    class Config:
        env_prefix = "CELERY_"


class GamingConfig(BaseSettings):
    """Gaming-specific configuration"""
    gaming_keywords: str = Field(
        default="play-to-earn,P2E,NFT gaming,GameFi,metaverse,blockchain gaming,crypto gaming,gaming tokens,gaming NFTs,virtual worlds"
    )
    p2e_games: str = Field(
        default="axie-infinity,splinterlands,gods-unchained,alien-worlds,the-sandbox,decentraland"
    )
    gaming_tokens: str = Field(
        default="AXS,SLP,SAND,MANA,ENJ,GALA,ILV,ALICE,TLM,SKILL"
    )
    
    # Gaming contract addresses
    axie_infinity_contract: str = Field(default="******************************************")
    sandbox_contract: str = Field(default="******************************************")
    decentraland_contract: str = Field(default="******************************************")
    
    @validator('gaming_keywords', 'p2e_games', 'gaming_tokens')
    def split_comma_separated(cls, v):
        if isinstance(v, str):
            return [item.strip() for item in v.split(',')]
        return v
    
    class Config:
        env_prefix = ""


class SecuritySettings(BaseSettings):
    """Security configuration"""
    secret_key: str = Field(default="your-secret-key-here")
    jwt_secret_key: str = Field(default="your-jwt-secret-key")
    jwt_algorithm: str = Field(default="HS256")
    jwt_expiration_hours: int = Field(default=24)
    
    class Config:
        env_prefix = ""


class MonitoringSettings(BaseSettings):
    """Monitoring configuration"""
    prometheus_port: int = Field(default=9090)
    log_level: str = Field(default="INFO")
    sentry_dsn: str = Field(default="")

    class Config:
        env_prefix = ""


class DataCollectionSettings(BaseSettings):
    """Data collection interval configuration"""
    # Gaming analytics collection interval (30 minutes)
    gaming_analytics_interval: int = Field(default=1800)

    # Social media collection interval (4 hours)
    social_media_interval: int = Field(default=14400)

    # Blockchain data collection interval (30 minutes)
    blockchain_data_interval: int = Field(default=1800)

    # News scraping interval (1 hour)
    news_scraping_interval: int = Field(default=3600)

    # WebSocket update interval (30 minutes)
    streaming_interval_seconds: int = Field(default=1800)

    # WebSocket configuration
    websocket_enabled: bool = Field(default=True)
    websocket_heartbeat_interval: int = Field(default=30)
    websocket_max_connections: int = Field(default=100)

    class Config:
        env_prefix = ""


class Settings(BaseSettings):
    """Main settings class"""
    environment: str = Field(default="development")

    # Sub-configurations
    database: DatabaseSettings = Field(default_factory=DatabaseSettings)
    redis: RedisSettings = Field(default_factory=RedisSettings)
    api: APISettings = Field(default_factory=APISettings)
    blockchain: BlockchainSettings = Field(default_factory=BlockchainSettings)
    social_media: SocialMediaSettings = Field(default_factory=SocialMediaSettings)
    news_api: NewsAPISettings = Field(default_factory=NewsAPISettings)
    gaming_api: GamingAPISettings = Field(default_factory=GamingAPISettings)
    blockchain_data: BlockchainDataSettings = Field(default_factory=BlockchainDataSettings)
    scraping: ScrapingSettings = Field(default_factory=ScrapingSettings)
    rate_limit: RateLimitSettings = Field(default_factory=RateLimitSettings)
    celery: CelerySettings = Field(default_factory=CelerySettings)
    gaming: GamingConfig = Field(default_factory=GamingConfig)
    security: SecuritySettings = Field(default_factory=SecuritySettings)
    monitoring: MonitoringSettings = Field(default_factory=MonitoringSettings)
    data_collection: DataCollectionSettings = Field(default_factory=DataCollectionSettings)

    # Direct access properties for convenience
    @property
    def gaming_analytics_interval(self) -> int:
        return self.data_collection.gaming_analytics_interval

    @property
    def social_media_interval(self) -> int:
        return self.data_collection.social_media_interval

    @property
    def blockchain_data_interval(self) -> int:
        return self.data_collection.blockchain_data_interval

    @property
    def news_scraping_interval(self) -> int:
        return self.data_collection.news_scraping_interval

    @property
    def streaming_interval_seconds(self) -> int:
        return self.data_collection.streaming_interval_seconds

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        extra = "ignore"  # Ignore extra fields from environment


# Global settings instance
settings = Settings()


def get_settings() -> Settings:
    """Get settings instance"""
    return settings
