"""
Alchemy API Client for Web3 Gaming Analytics
Comprehensive client for Portfolio, NFT, Token, and Transfer APIs
"""
import asyncio
import logging
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timedelta
from dataclasses import dataclass
import json

from .base import BaseBlockchainDataClient
from config.settings import get_settings

settings = get_settings()
logger = logging.getLogger(__name__)


@dataclass
class AlchemyNetworkConfig:
    """Network configuration for Alchemy API"""
    name: str
    alchemy_id: str
    chain_id: int
    native_token: str
    gaming_focus: bool = False


class AlchemyClient(BaseBlockchainDataClient):
    """Enhanced Alchemy API client for gaming analytics"""
    
    # Network mappings for gaming-focused chains
    NETWORK_CONFIGS = {
        "ethereum": AlchemyNetworkConfig("Ethereum", "eth-mainnet", 1, "ETH", True),
        "polygon": AlchemyNetworkConfig("Polygon", "polygon-mainnet", 137, "MATIC", True),
        "arbitrum": AlchemyNetworkConfig("Arbitrum", "arbitrum-mainnet", 42161, "ETH", True),
        "optimism": AlchemyNetworkConfig("Optimism", "optimism-mainnet", 10, "ETH", True),
        "base": AlchemyNetworkConfig("Base", "base-mainnet", 8453, "ETH", True),
        "bsc": AlchemyNetworkConfig("BSC", "bsc-mainnet", 56, "BNB", True),
    }
    
    def __init__(self):
        super().__init__(
            api_key=settings.blockchain_data.alchemy_api_key,
            base_url=settings.blockchain_data.alchemy_base_url,
            rate_limit=settings.blockchain_data.rate_limit_per_minute
        )
        self.supported_networks = settings.blockchain_data.alchemy_supported_networks
        self._cache = {}
        self._cache_ttl = timedelta(minutes=5)  # 5-minute cache for API responses
    
    def _get_auth_headers(self) -> Dict[str, str]:
        """Get authentication headers for Alchemy API"""
        return {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        }
    
    def _get_api_url(self, endpoint: str) -> str:
        """Construct full API URL with API key"""
        return f"{self.base_url}/{self.api_key}/{endpoint}"
    
    def _is_cache_valid(self, cache_key: str) -> bool:
        """Check if cached data is still valid"""
        if cache_key not in self._cache:
            return False
        
        cached_time, _ = self._cache[cache_key]
        return datetime.utcnow() - cached_time < self._cache_ttl
    
    def _get_cached_data(self, cache_key: str) -> Optional[Any]:
        """Get cached data if valid"""
        if self._is_cache_valid(cache_key):
            _, data = self._cache[cache_key]
            return data
        return None
    
    def _cache_data(self, cache_key: str, data: Any):
        """Cache data with timestamp"""
        self._cache[cache_key] = (datetime.utcnow(), data)
    
    async def _test_endpoint(self):
        """Test Alchemy API connection with a simple request"""
        # Test with a simple token metadata request
        test_url = self._get_api_url("assets/tokens/by-address")
        test_payload = {
            "addresses": [{
                "address": "******************************************",  # Zero address
                "networks": ["eth-mainnet"]
            }],
            "withMetadata": False,
            "withPrices": False
        }
        
        response = await self._make_request('POST', test_url, data=test_payload)
        if 'data' not in response:
            raise Exception("Invalid response format from Alchemy API")
    
    async def get_gaming_tokens_data(self, tokens: List[str]) -> List[Dict[str, Any]]:
        """Get gaming token data using Alchemy Portfolio API"""
        if not tokens:
            return []
        
        cache_key = f"gaming_tokens_{hash(tuple(sorted(tokens)))}"
        cached_data = self._get_cached_data(cache_key)
        if cached_data:
            return cached_data
        
        # For token data, we need contract addresses, not symbols
        # This is a limitation - we'll need to maintain a mapping or use token metadata
        logger.warning("Alchemy requires contract addresses, not symbols. Consider implementing token symbol to address mapping.")
        
        # Return empty for now - this will be enhanced in Phase 2
        result = []
        self._cache_data(cache_key, result)
        return result
    
    async def get_gaming_wallet_portfolio(
        self, 
        addresses: List[str], 
        networks: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """Get comprehensive gaming wallet portfolio data"""
        if not addresses:
            return {}
        
        if not networks:
            networks = ["eth-mainnet", "polygon-mainnet"]  # Default gaming networks
        
        cache_key = f"wallet_portfolio_{hash(tuple(sorted(addresses)))}_{hash(tuple(sorted(networks)))}"
        cached_data = self._get_cached_data(cache_key)
        if cached_data:
            return cached_data
        
        # Prepare request payload
        address_network_pairs = []
        for address in addresses[:2]:  # Alchemy limit: 2 addresses
            for network in networks[:5]:  # Alchemy limit: 5 networks per address
                if network in self.supported_networks:
                    address_network_pairs.append({
                        "address": address,
                        "networks": [network]
                    })
        
        if not address_network_pairs:
            return {}
        
        payload = {
            "addresses": address_network_pairs,
            "withMetadata": True,
            "withPrices": True,
            "includeNativeTokens": True
        }
        
        try:
            url = self._get_api_url("assets/tokens/by-address")
            response = await self._make_request('POST', url, data=payload)
            
            result = {
                'addresses': addresses,
                'networks': networks,
                'portfolio_data': response.get('data', []),
                'timestamp': datetime.utcnow().isoformat()
            }
            
            self._cache_data(cache_key, result)
            return result
            
        except Exception as e:
            logger.error(f"Failed to get gaming wallet portfolio: {e}")
            return {}
    
    async def get_transaction_history(
        self, 
        addresses: List[str], 
        networks: Optional[List[str]] = None,
        limit: int = 25
    ) -> Dict[str, Any]:
        """Get transaction history for gaming wallets"""
        if not addresses:
            return {}
        
        if not networks:
            networks = ["eth-mainnet"]  # Currently limited to Ethereum and Base
        
        cache_key = f"tx_history_{hash(tuple(sorted(addresses)))}_{hash(tuple(sorted(networks)))}"
        cached_data = self._get_cached_data(cache_key)
        if cached_data:
            return cached_data
        
        # Alchemy currently limits to 1 address for transaction history
        address = addresses[0]
        network = networks[0] if networks[0] in ["eth-mainnet", "base-mainnet"] else "eth-mainnet"
        
        payload = {
            "addresses": [{
                "address": address,
                "networks": [network]
            }],
            "limit": min(limit, 50)  # Alchemy max: 50
        }
        
        try:
            url = self._get_api_url("transactions/history/by-address")
            response = await self._make_request('POST', url, data=payload)
            
            result = {
                'address': address,
                'network': network,
                'transactions': response.get('transactions', []),
                'total_count': response.get('totalCount', 0),
                'timestamp': datetime.utcnow().isoformat()
            }
            
            self._cache_data(cache_key, result)
            return result
            
        except Exception as e:
            logger.error(f"Failed to get transaction history: {e}")
            return {}
    
    async def get_nft_collection_data(self, collection_address: str) -> Dict[str, Any]:
        """Get NFT collection data using Alchemy NFT API"""
        if not collection_address:
            return {}
        
        cache_key = f"nft_collection_{collection_address}"
        cached_data = self._get_cached_data(cache_key)
        if cached_data:
            return cached_data
        
        # This will be implemented in Phase 2 with NFT API endpoints
        logger.info(f"NFT collection data for {collection_address} - to be implemented in Phase 2")
        
        result = {
            'collection_address': collection_address,
            'placeholder': True,
            'message': 'NFT collection data will be implemented in Phase 2'
        }
        
        self._cache_data(cache_key, result)
        return result
    
    async def get_gaming_protocol_metrics(self, protocol_name: str) -> Dict[str, Any]:
        """Get gaming protocol metrics"""
        cache_key = f"protocol_metrics_{protocol_name}"
        cached_data = self._get_cached_data(cache_key)
        if cached_data:
            return cached_data
        
        # This will be enhanced in Phase 2 with comprehensive protocol analysis
        logger.info(f"Gaming protocol metrics for {protocol_name} - to be enhanced in Phase 2")
        
        result = {
            'protocol_name': protocol_name,
            'placeholder': True,
            'message': 'Gaming protocol metrics will be enhanced in Phase 2'
        }
        
        self._cache_data(cache_key, result)
        return result
    
    def get_supported_networks(self) -> List[str]:
        """Get list of supported networks for gaming analytics"""
        return list(self.NETWORK_CONFIGS.keys())
    
    def get_network_config(self, network: str) -> Optional[AlchemyNetworkConfig]:
        """Get network configuration"""
        return self.NETWORK_CONFIGS.get(network)
    
    async def health_check(self) -> Dict[str, Any]:
        """Comprehensive health check for Alchemy client"""
        try:
            await self._test_endpoint()
            return {
                'status': 'healthy',
                'api_key_configured': bool(self.api_key),
                'supported_networks': len(self.supported_networks),
                'cache_entries': len(self._cache),
                'timestamp': datetime.utcnow().isoformat()
            }
        except Exception as e:
            return {
                'status': 'unhealthy',
                'error': str(e),
                'timestamp': datetime.utcnow().isoformat()
            }


# Global instance for easy access
alchemy_client = AlchemyClient()
